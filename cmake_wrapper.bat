@echo off
REM CMake wrapper to fix Visual Studio generator for Flutter
REM This script intercepts CMake calls and replaces "Visual Studio 16 2019" with "Visual Studio 17 2022"

set "REAL_CMAKE=C:\Program Files\Microsoft Visual Studio\18\Insiders\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe"

REM Build the command line by replacing the generator
set "CMDLINE="
:loop
if "%~1"=="" goto :execute
if "%~1"=="Visual Studio 16 2019" (
    set "CMDLINE=%CMDLINE% "Visual Studio 17 2022""
) else (
    set "CMDLINE=%CMDLINE% "%~1""
)
shift
goto :loop

:execute
REM Execute the real CMake with modified arguments
echo Executing: "%REAL_CMAKE%" %CMDLINE%
"%REAL_CMAKE%" %CMDLINE%
